.file-upload-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 20px 0;
  color: white;
}

.type-selector {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.type-label {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  min-width: 70px;
}

.upload-area {
  position: relative;
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.upload-area:hover {
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.1);
}

.upload-area.secondary {
  padding: 30px 20px;
  border-style: solid;
  border-width: 1px;
}

.file-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s ease;
}

.upload-label:hover {
  color: white;
}

.upload-icon {
  width: 32px;
  height: 32px;
}

.upload-text {
  font-size: 16px;
  font-weight: 500;
}

.file-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 12px 16px;
  margin-top: 15px;
}

.file-details {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.file-icon {
  width: 20px;
  height: 20px;
  color: rgba(255, 255, 255, 0.7);
}

.file-meta {
  flex: 1;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: white;
  margin-bottom: 4px;
  word-break: break-all;
}

.file-stats {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.file-size,
.file-type {
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

.remove-btn {
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 6px;
  padding: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #fca5a5;
}

.remove-btn:hover {
  background: rgba(239, 68, 68, 0.3);
  border-color: rgba(239, 68, 68, 0.5);
}

.remove-icon {
  width: 16px;
  height: 16px;
}

.second-file-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.second-file-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 15px 0;
  color: rgba(255, 255, 255, 0.9);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-area {
    padding: 30px 15px;
  }
  
  .upload-icon {
    width: 28px;
    height: 28px;
  }
  
  .upload-text {
    font-size: 14px;
  }
  
  .file-info {
    padding: 10px 12px;
  }
  
  .file-stats {
    flex-direction: column;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .type-selector {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .upload-area {
    padding: 25px 10px;
  }
  
  .file-name {
    font-size: 13px;
  }
}
