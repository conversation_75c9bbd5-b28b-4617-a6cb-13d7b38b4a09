.log-output {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.log-output .card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.log-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.log-title h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: white;
}

.terminal-icon {
  width: 20px;
  height: 20px;
  color: rgba(255, 255, 255, 0.8);
}

.clear-btn {
  padding: 6px 12px;
  font-size: 13px;
}

.clear-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.log-container {
  flex: 1;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 15px;
  overflow-y: auto;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.log-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
}

.empty-terminal-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 12px;
  opacity: 0.5;
}

.empty-description {
  font-size: 12px;
  margin-top: 4px;
}

.log-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.log-entry {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  animation: fadeInUp 0.3s ease;
}

.log-entry:last-child {
  border-bottom: none;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.log-time {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
  min-width: 70px;
  font-weight: 500;
}

.log-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  flex: 1;
}

.log-icon {
  width: 14px;
  height: 14px;
  margin-top: 1px;
  flex-shrink: 0;
}

.log-icon.success {
  color: #10b981;
}

.log-icon.error {
  color: #ef4444;
}

.log-icon.info {
  color: #3b82f6;
}

.log-message {
  color: rgba(255, 255, 255, 0.9);
  word-break: break-word;
  line-height: 1.4;
}

.log-entry.success .log-message {
  color: #86efac;
}

.log-entry.error .log-message {
  color: #fca5a5;
}

.log-entry.info .log-message {
  color: rgba(255, 255, 255, 0.9);
}

.log-footer {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.log-count {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.btn-icon {
  width: 14px;
  height: 14px;
}

/* 滚动条样式 */
.log-container::-webkit-scrollbar {
  width: 6px;
}

.log-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.log-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.log-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .log-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .log-title {
    justify-content: center;
  }
  
  .log-container {
    font-size: 12px;
    padding: 12px;
  }
  
  .log-entry {
    gap: 8px;
  }
  
  .log-time {
    min-width: 60px;
    font-size: 10px;
  }
  
  .log-icon {
    width: 12px;
    height: 12px;
  }
}

@media (max-width: 480px) {
  .log-container {
    padding: 10px;
  }
  
  .log-entry {
    flex-direction: column;
    gap: 4px;
  }
  
  .log-time {
    min-width: auto;
  }
  
  .log-content {
    gap: 6px;
  }
}
