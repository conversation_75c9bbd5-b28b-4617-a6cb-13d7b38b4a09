{"name": "file-processor-frontend", "version": "1.0.0", "description": "React-based file processor frontend", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"clsx": "^2.0.0", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "4", "@yarnpkg/esbuild-plugin-pnp": "^3.0.0-rc.15", "@yarnpkg/sdks": "^3.2.2", "esbuild": "0.21", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "vite": "5"}, "volta": {"node": "22.18.0", "yarn": "4.9.2"}}