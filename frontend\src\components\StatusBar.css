.status-bar {
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 200px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-indicator:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.status-bar.editing .status-indicator {
  background: rgba(255, 255, 255, 0.2);
  cursor: default;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
}

.status-dot.connecting {
  background: #fbbf24;
}

.status-dot.connected {
  background: #10b981;
}

.status-dot.failed {
  background: #ef4444;
}

.status-icon {
  width: 8px;
  height: 8px;
  color: white;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.status-content {
  flex: 1;
  min-height: 20px;
  display: flex;
  align-items: center;
}

.status-text {
  font-size: 14px;
  color: white;
  white-space: nowrap;
  text-transform: none !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.status-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 4px 8px;
  color: white;
  font-size: 14px;
  outline: none;
  transition: all 0.3s ease;
  min-width: 150px;
}

.status-input:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

.status-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.status-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.edit-icon {
  width: 14px;
  height: 14px;
  color: rgba(255, 255, 255, 0.6);
  transition: all 0.2s ease;
}

.status-indicator:hover .edit-icon {
  color: rgba(255, 255, 255, 0.8);
}

.status-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transform: translateX(10px);
  animation: slideIn 0.3s ease forwards;
}

@keyframes slideIn {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.status-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.status-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.status-btn:active {
  transform: scale(0.95);
}

.status-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.status-btn.confirm {
  background: rgba(34, 197, 94, 0.3);
  border-color: rgba(34, 197, 94, 0.5);
}

.status-btn.confirm:hover:not(:disabled) {
  background: rgba(34, 197, 94, 0.5);
  border-color: rgba(34, 197, 94, 0.7);
}

.status-btn.cancel {
  background: rgba(239, 68, 68, 0.3);
  border-color: rgba(239, 68, 68, 0.5);
}

.status-btn.cancel:hover:not(:disabled) {
  background: rgba(239, 68, 68, 0.5);
  border-color: rgba(239, 68, 68, 0.7);
}

.btn-icon {
  width: 14px;
  height: 14px;
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-indicator {
    min-width: 180px;
    padding: 6px 12px;
  }
  
  .status-text {
    font-size: 13px;
  }
  
  .status-input {
    font-size: 13px;
    min-width: 120px;
  }
  
  .status-btn {
    width: 24px;
    height: 24px;
  }
  
  .btn-icon {
    width: 12px;
    height: 12px;
  }
}

@media (max-width: 480px) {
  .status-indicator {
    min-width: 150px;
    padding: 6px 10px;
  }
  
  .status-text {
    font-size: 12px;
  }
  
  .status-input {
    font-size: 12px;
    min-width: 100px;
  }
}
