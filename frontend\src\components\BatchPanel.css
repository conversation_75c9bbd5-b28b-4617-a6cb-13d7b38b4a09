.batch-panel {
  margin-top: 20px;
}

.batch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.7);
}

.empty-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto 15px;
  opacity: 0.5;
}

.empty-description {
  font-size: 14px;
  margin-top: 5px;
}

.batch-tasks {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.batch-task {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 15px;
}

.task-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.task-name {
  flex: 1;
  font-weight: 500;
}

.btn-icon-only {
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-task {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.remove-task:hover {
  background: rgba(239, 68, 68, 0.2);
}

.task-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.file-section,
.operation-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.section-label {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.file-input-hidden {
  display: none;
}

.file-select-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  width: fit-content;
}

.file-select-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 10px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
}

.file-icon {
  width: 16px;
  height: 16px;
  color: rgba(255, 255, 255, 0.7);
}

.file-name {
  flex: 1;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  word-break: break-all;
}

.remove-file {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.remove-file:hover {
  background: rgba(239, 68, 68, 0.2);
}

.batch-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .batch-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .task-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .file-item {
    padding: 6px 10px;
  }
  
  .file-name {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .batch-task {
    padding: 12px;
  }
  
  .empty-state {
    padding: 30px 15px;
  }
  
  .empty-icon {
    width: 40px;
    height: 40px;
  }
}
