"""
最简单的Celery测试任务
"""
import logging
import time

logger = logging.getLogger(__name__)

def simple_test_task(self):
    """最简单的测试任务"""
    try:
        logger.info("=== SIMPLE TEST TASK STARTED ===")
        logger.info(f"Task ID: {self.request.id if hasattr(self, 'request') else 'unknown'}")
        
        # 更新状态
        logger.info("Updating task state...")
        self.update_state(state='PROGRESS', meta={'current': 50, 'total': 100, 'status': 'Testing...'})
        
        # 简单等待
        logger.info("Waiting 2 seconds...")
        time.sleep(2)
        
        # 完成
        logger.info("Task completed successfully!")
        result = {
            'status': 'success',
            'message': 'Simple test task completed',
            'test_data': 'Hello from Celery!'
        }
        
        self.update_state(state='SUCCESS', meta=result)
        return result
        
    except Exception as e:
        logger.error(f"Simple test task failed: {str(e)}")
        raise Exception(f"Test task failed: {str(e)}")
