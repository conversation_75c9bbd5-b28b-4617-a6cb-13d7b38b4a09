import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react'
import { useDebouncedCallback } from '../hooks/useDebounce'

const ApiContext = createContext()

export const useApi = () => {
  const context = useContext(ApiContext)
  if (!context) {
    throw new Error('useApi must be used within an ApiProvider')
  }
  return context
}

export const ApiProvider = ({ children }) => {
  const [apiBase, setApiBase] = useState('http://localhost:5000/api')
  const [connectionStatus, setConnectionStatus] = useState('connecting')
  const [isConnected, setIsConnected] = useState(false)
  const connectionCheckRef = useRef(null)

  // 检测是否在Electron环境中
  const isElectron = window.electronAPI && window.electronAPI.isElectron

  // 设置全局API_BASE变量
  React.useEffect(() => {
    window.API_BASE = apiBase
  }, [apiBase])

  // 加载保存的API配置或从Electron获取
  useEffect(() => {
    const initializeApiBase = async () => {
      if (isElectron) {
        // 在Electron环境中，从主进程获取后端URL
        try {
          const backendUrl = await window.electronAPI.getBackendUrl()
          const apiUrl = `${backendUrl}/api`
          console.log('Electron环境检测到，使用后端URL:', apiUrl)
          setApiBase(apiUrl)
          return
        } catch (error) {
          console.error('获取Electron后端URL失败:', error)
        }
      }

      // 非Electron环境或获取失败时，使用保存的配置
      const savedConfig = localStorage.getItem('api_config')
      if (savedConfig) {
        try {
          const config = JSON.parse(savedConfig)
          if (config.baseUrl) {
            setApiBase(config.baseUrl)
          }
        } catch (error) {
          console.error('加载API配置失败:', error)
        }
      }
    }

    initializeApiBase()
  }, [])

  // 保存API配置
  const saveApiConfig = (baseUrl) => {
    try {
      const config = { baseUrl }
      localStorage.setItem('api_config', JSON.stringify(config))
      setApiBase(baseUrl)
      return true
    } catch (error) {
      console.error('保存API配置失败:', error)
      return false
    }
  }

  // 检查连接状态（内部函数，不防抖）
  const _checkConnection = useCallback(async (urlToCheck = null) => {
    const targetUrl = urlToCheck || apiBase
    console.log('开始检查连接:', targetUrl)

    // 如果有正在进行的检查，取消它
    if (connectionCheckRef.current) {
      connectionCheckRef.current.abort()
    }

    // 创建新的AbortController
    const controller = new AbortController()
    connectionCheckRef.current = controller

    setConnectionStatus('connecting')
    setIsConnected(false)

    try {
      const response = await fetch(`${targetUrl}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        mode: 'cors',
        cache: 'no-cache',
        signal: controller.signal
      })

      console.log('连接响应:', response.status, response.ok)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.log('健康检查响应:', data)

      if (data.status === 'healthy') {
        console.log('连接成功')
        setConnectionStatus('connected')
        setIsConnected(true)
        return true
      } else {
        throw new Error('服务器状态异常')
      }
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log('连接检查被取消')
        return false
      }
      console.error('连接检查失败:', error)
      setConnectionStatus('failed')
      setIsConnected(false)
      return false
    } finally {
      connectionCheckRef.current = null
    }
  }, [apiBase])

  // 防抖的连接检查函数
  const debouncedCheckConnection = useDebouncedCallback(_checkConnection, 300, [apiBase])

  // 公开的连接检查函数
  const checkConnection = useCallback(() => {
    return _checkConnection()
  }, [_checkConnection])

  // 更新API地址
  const updateApiBase = async (newUrl) => {
    const oldUrl = apiBase

    // 格式化URL
    let formattedUrl = newUrl.trim()
    if (!formattedUrl.startsWith('http://') && !formattedUrl.startsWith('https://')) {
      formattedUrl = 'http://' + formattedUrl
    }
    if (!formattedUrl.endsWith('/api')) {
      formattedUrl += '/api'
    }

    console.log('更新API地址:', oldUrl, '->', formattedUrl)

    // 临时设置新URL进行测试
    setApiBase(formattedUrl)

    // 等待一个微任务，确保状态更新完成
    await new Promise(resolve => setTimeout(resolve, 0))

    try {
      // 直接测试新URL，不依赖状态
      const response = await fetch(`${formattedUrl}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        mode: 'cors',
        cache: 'no-cache'
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.status === 'healthy') {
        console.log('新URL连接成功，保存配置')
        setConnectionStatus('connected')
        setIsConnected(true)
        saveApiConfig(formattedUrl)
        return { success: true, message: '连接成功' }
      } else {
        throw new Error('服务器状态异常')
      }
    } catch (error) {
      console.error('新URL连接失败，恢复原URL:', error)
      // 恢复原URL和状态
      setApiBase(oldUrl)
      setConnectionStatus('failed')
      setIsConnected(false)
      return { success: false, message: error.message }
    }
  }

  // 获取操作列表
  const getOperations = async (type) => {
    try {
      const response = await fetch(`${apiBase}/operations`)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }
      const data = await response.json()
      // 从完整的操作列表中提取指定类型的操作
      return data.operations[type] || []
    } catch (error) {
      console.error('获取操作列表失败:', error)
      return []
    }
  }

  // 上传文件并处理（使用批量处理API）
  const processFile = async (file, operation, parameters = {}, secondFile = null) => {
    try {
      // 1. 先上传文件
      const formData = new FormData()
      formData.append('files', file)
      if (secondFile) {
        formData.append('files', secondFile)
      }
      formData.append('file_type', 'image') // 默认为图像类型

      const uploadResponse = await fetch(`${apiBase}/upload`, {
        method: 'POST',
        body: formData
      })

      if (!uploadResponse.ok) {
        throw new Error(`File upload failed: HTTP ${uploadResponse.status}`)
      }

      const uploadResult = await uploadResponse.json()
      const uploadedFiles = uploadResult.files || []

      if (uploadedFiles.length === 0) {
        throw new Error('No files uploaded successfully')
      }

      // 2. 提交批量处理任务
      const batchData = {
        files: uploadedFiles,
        operation: operation,
        parameters: parameters
      }

      const batchResponse = await fetch(`${apiBase}/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(batchData)
      })

      if (!batchResponse.ok) {
        throw new Error(`Batch processing failed: HTTP ${batchResponse.status}`)
      }

      return await batchResponse.json()
    } catch (error) {
      console.error('processFile error:', error)
      throw error
    }
  }

  // 处理视频文件（使用批量处理API）
  const processVideo = async (filePath, operation, parameters = {}) => {
    try {
      // 构造批量处理数据格式
      // 假设filePath是已上传文件的路径或文件信息
      const fileInfo = typeof filePath === 'string'
        ? { stored_filename: filePath.split('/').pop(), filename: filePath.split('/').pop() }
        : filePath

      const batchData = {
        files: [fileInfo],
        operation: operation,
        parameters: parameters
      }

      const response = await fetch(`${apiBase}/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(batchData)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error('processVideo error:', error)
      throw error
    }
  }

  const value = {
    apiBase,
    connectionStatus,
    isConnected,
    checkConnection,
    updateApiBase,
    getOperations,
    processFile,
    processVideo,
    saveApiConfig
  }

  return (
    <ApiContext.Provider value={value}>
      {children}
    </ApiContext.Provider>
  )
}
