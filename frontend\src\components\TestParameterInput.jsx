import React, { useState } from 'react';
import './OperationPanel.css';

const TestParameterInput = () => {
  const [parameters, setParameters] = useState({});

  const handleParameterChange = (paramName, value) => {
    setParameters({
      ...parameters,
      [paramName]: value
    });
  };

  const renderParameterInput = (paramName) => {
    const value = parameters[paramName] || '';
    
    // 获取参数的默认值和范围
    const getParameterConfig = (param) => {
      const configs = {
        // 视频尺寸参数
        'width': { type: 'range', min: 320, max: 1920, default: 640, step: 1, unit: 'px' },
        'height': { type: 'range', min: 240, max: 1080, default: 480, step: 1, unit: 'px' },
        
        // 阈值参数
        'threshold': { type: 'range', min: 0, max: 255, default: 127, step: 1, unit: '' },
        'max_value': { type: 'range', min: 0, max: 255, default: 255, step: 1, unit: '' },
        
        // 滤波参数
        'kernel_size': { type: 'range', min: 1, max: 51, default: 5, step: 2, unit: 'px' },
        'sigma': { type: 'range', min: 0.1, max: 10.0, default: 1.0, step: 0.1, unit: '' },
        
        // 下拉选择参数
        'filter_type': {
          type: 'select',
          options: [
            { value: 'gaussian', label: '高斯滤波' },
            { value: 'median', label: '中值滤波' },
            { value: 'bilateral', label: '双边滤波' },
            { value: 'sharpen', label: '锐化滤波' }
          ],
          default: 'gaussian'
        },
        'threshold_type': {
          type: 'select',
          options: [
            { value: 'binary', label: '二值化' },
            { value: 'binary_inv', label: '反二值化' },
            { value: 'trunc', label: '截断' },
            { value: 'tozero', label: '阈值化为零' },
            { value: 'tozero_inv', label: '反阈值化为零' }
          ],
          default: 'binary'
        },
        'save_video': {
          type: 'checkbox',
          default: true,
          label: '保存视频文件'
        }
      };
      return configs[param] || { type: 'text', default: '' };
    };

    const config = getParameterConfig(paramName);
    const currentValue = value || config.default;

    console.log('Rendering parameter:', paramName, 'config:', config, 'value:', currentValue);

    // 渲染滑块组件
    if (config.type === 'range') {
      return (
        <div className="parameter-range">
          <div className="range-header">
            <span className="range-label">{paramName}</span>
            <span className="range-value">{currentValue}{config.unit}</span>
          </div>
          <input
            type="range"
            className="range-slider"
            min={config.min}
            max={config.max}
            step={config.step}
            value={currentValue}
            onChange={(e) => handleParameterChange(paramName, parseFloat(e.target.value))}
          />
          <div className="range-limits">
            <span>{config.min}{config.unit}</span>
            <span>{config.max}{config.unit}</span>
          </div>
        </div>
      );
    }

    // 渲染下拉选择组件
    if (config.type === 'select') {
      return (
        <div className="parameter-select">
          <label className="select-label">{paramName}:</label>
          <select
            className="select"
            value={currentValue}
            onChange={(e) => handleParameterChange(paramName, e.target.value)}
          >
            {config.options.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      );
    }

    // 渲染复选框组件
    if (config.type === 'checkbox') {
      return (
        <div className="parameter-checkbox">
          <label className="checkbox-label">
            <input
              type="checkbox"
              checked={currentValue}
              onChange={(e) => handleParameterChange(paramName, e.target.checked)}
            />
            <span className="checkbox-text">{config.label}</span>
          </label>
        </div>
      );
    }

    // 默认文本输入
    return (
      <input
        type="text"
        className="input"
        value={currentValue}
        onChange={(e) => handleParameterChange(paramName, e.target.value)}
        placeholder={`输入${paramName}`}
      />
    );
  };

  const testParameters = [
    'threshold',
    'sigma', 
    'filter_type',
    'threshold_type',
    'save_video',
    'unknown_param'
  ];

  return (
    <div style={{ padding: '20px', background: '#1a1a1a', color: 'white', minHeight: '100vh' }}>
      <h1>🧪 参数输入组件测试</h1>
      <p>当前参数值: {JSON.stringify(parameters, null, 2)}</p>
      
      {testParameters.map(param => (
        <div key={param} style={{ marginBottom: '20px' }}>
          <h3>{param}:</h3>
          {renderParameterInput(param)}
        </div>
      ))}
    </div>
  );
};

export default TestParameterInput;
