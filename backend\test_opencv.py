#!/usr/bin/env python3
"""
测试OpenCV在Windows上读取文件的问题
"""
import cv2
import os
import sys

def test_opencv_read():
    """测试OpenCV读取文件"""
    # 测试文件路径
    test_file = r"E:\Projects\self-made\clover\backend\uploads\test_image.jpg"
    
    print(f"Testing file: {test_file}")
    print(f"File exists: {os.path.exists(test_file)}")
    print(f"File is file: {os.path.isfile(test_file)}")
    
    if os.path.exists(test_file):
        print(f"File size: {os.path.getsize(test_file)} bytes")
        
        # 尝试用OpenCV读取
        print("Attempting to read with cv2.imread...")
        image = cv2.imread(test_file)
        
        if image is None:
            print("❌ cv2.imread returned None!")
            
            # 尝试不同的读取方式
            print("Trying with different flags...")
            image2 = cv2.imread(test_file, cv2.IMREAD_COLOR)
            print(f"cv2.IMREAD_COLOR: {image2 is not None}")
            
            image3 = cv2.imread(test_file, cv2.IMREAD_UNCHANGED)
            print(f"cv2.IMREAD_UNCHANGED: {image3 is not None}")
            
            # 尝试使用正斜杠路径
            test_file_unix = test_file.replace('\\', '/')
            print(f"Trying Unix-style path: {test_file_unix}")
            image4 = cv2.imread(test_file_unix)
            print(f"Unix path result: {image4 is not None}")
            
        else:
            print(f"✅ Successfully read image: {image.shape}")
    
    else:
        print("❌ Test file does not exist!")

if __name__ == "__main__":
    test_opencv_read()
