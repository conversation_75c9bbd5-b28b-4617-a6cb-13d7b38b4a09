.batch-results {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.batch-results .card {
  width: 90vw;
  max-width: 1000px;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  animation: slideIn 0.3s ease;
  overflow: hidden;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.results-title h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: white;
}

.results-summary {
  display: flex;
  gap: 15px;
  font-size: 14px;
}

.success-count {
  color: #10b981;
}

.failure-count {
  color: #ef4444;
}

.processing-count {
  color: #3b82f6;
}

.total-count {
  color: rgba(255, 255, 255, 0.7);
}

/* 整体进度样式 */
.overall-progress {
  margin-bottom: 24px;
  padding: 16px;
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.7);
}

.loading-icon,
.error-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.error-icon {
  color: #ef4444;
}

.results-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 500px;
  padding-right: 8px;
}

/* 任务组样式 */
.task-group {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
  flex-shrink: 0;
  min-height: auto;
}

.task-group.success {
  border-left: 4px solid #10b981;
}

.task-group.error {
  border-left: 4px solid #ef4444;
}

.task-group.pending {
  border-left: 4px solid #f59e0b;
}

.task-header {
  background: rgba(255, 255, 255, 0.05);
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.task-status {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 2px;
}

.task-summary {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.task-results {
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-height: auto;
  max-height: none;
  flex-shrink: 0;
}

/* 任务组样式 */
.task-group {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.task-group.success {
  border-left: 4px solid #10b981;
}

.task-group.error {
  border-left: 4px solid #ef4444;
}

.task-group.pending {
  border-left: 4px solid #f59e0b;
}

.task-header {
  background: rgba(255, 255, 255, 0.05);
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.task-status {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 2px;
}

.task-summary {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.task-results {
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.result-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
  flex-shrink: 0;
  min-height: auto;
}

.result-item:hover {
  background: rgba(255, 255, 255, 0.08);
}

.result-item.success {
  border-left: 4px solid #10b981;
}

.result-item.error {
  border-left: 4px solid #ef4444;
}

.result-item.pending {
  border-left: 4px solid #f59e0b;
}

.result-item.processing {
  border-left: 4px solid #3b82f6;
}

.result-status {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.status-icon {
  width: 20px;
  height: 20px;
}

.status-icon.success {
  color: #10b981;
}

.status-icon.error {
  color: #ef4444;
}

.status-icon.pending {
  color: #f59e0b;
}

.status-icon.processing {
  color: #3b82f6;
  animation: spin 1s linear infinite;
}

.status-info {
  flex: 1;
}

.task-id {
  font-size: 14px;
  font-weight: 500;
  color: white;
  margin-bottom: 2px;
}

.status-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
}

.result-content {
  margin-left: 32px;
}

.success-content {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.result-preview {
  flex-shrink: 0;
  width: 120px;
  height: 120px;
}

.image-preview {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.image-preview:hover {
  transform: scale(1.02);
  border-color: rgba(255, 255, 255, 0.2);
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.preview-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
  gap: 8px;
}

.preview-icon {
  width: 24px;
  height: 24px;
}

.result-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
}

.original-file,
.output-file,
.processing-time {
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.result-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.result-actions .btn {
  padding: 6px 12px;
  font-size: 12px;
}

/* 原文件信息tooltip */
.original-files-tooltip {
  position: relative;
  display: inline-block;
}

.files-count {
  color: #3b82f6;
  cursor: help;
  text-decoration: underline;
  text-decoration-style: dotted;
}

.original-files-tooltip:hover .tooltip-content {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.tooltip-content {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-8px);
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 12px 16px;
  min-width: 180px;
  max-width: 400px;
  width: max-content;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  white-space: normal;
}

.tooltip-content::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
}

.files-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  max-height: 250px;
  overflow-y: auto;
  word-break: break-word;
}

.file-item {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
  padding: 2px 0;
  padding: 4px 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  word-break: break-all;
  white-space: normal;
  line-height: 1.3;
}

.files-list::-webkit-scrollbar {
  width: 4px;
}

.files-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.files-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.error-content {
  color: #fca5a5;
  font-size: 13px;
  background: rgba(239, 68, 68, 0.1);
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.pending-content {
  color: rgba(255, 255, 255, 0.6);
  font-size: 13px;
  font-style: italic;
}

.empty-results {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.5);
}

.btn-icon {
  width: 14px;
  height: 14px;
}

/* 滚动条样式 */
.results-list::-webkit-scrollbar {
  width: 6px;
}

.results-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.results-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.results-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .batch-results .card {
    width: 95vw;
    max-height: 85vh;
    margin: 20px;
  }
  
  .results-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .results-summary {
    justify-content: space-between;
  }
  
  .success-content {
    flex-direction: column;
    gap: 12px;
  }
  
  .result-actions {
    justify-content: flex-start;
  }
  
  .results-list {
    max-height: 400px;
  }
}

@media (max-width: 480px) {
  .result-item {
    padding: 12px;
  }
  
  .result-content {
    margin-left: 0;
    margin-top: 8px;
  }
  
  .result-status {
    margin-bottom: 8px;
  }
  
  .file-info {
    font-size: 12px;
  }
  
  .result-actions .btn {
    padding: 8px 12px;
    font-size: 11px;
  }
}
