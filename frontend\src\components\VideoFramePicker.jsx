import React, { useState, useRef, useEffect } from 'react'
import { Play, Pause, SkipBack, SkipForward, Check, X } from 'lucide-react'
import './VideoFramePicker.css'

const VideoFramePicker = ({
  videoFile,
  onFrameSelect,
  onClose,
  initialFrame = 0
}) => {
  const videoRef = useRef(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [currentFrame, setCurrentFrame] = useState(initialFrame)
  const [fps, setFps] = useState(30) // 默认30fps，后续可以从视频元数据获取
  const [videoUrl, setVideoUrl] = useState(null)
  const [showControls, setShowControls] = useState(true) // 控制条显示状态
  const [hideControlsTimeout, setHideControlsTimeout] = useState(null)

  // 创建视频URL
  useEffect(() => {
    if (videoFile) {
      const url = URL.createObjectURL(videoFile)
      setVideoUrl(url)
      return () => URL.revokeObjectURL(url)
    }
  }, [videoFile])

  // 视频加载完成后的处理
  const handleLoadedMetadata = () => {
    const video = videoRef.current
    if (video) {
      setDuration(video.duration)
      // 尝试获取实际帧率，如果无法获取则使用默认值
      // 注意：HTML5 video API 无法直接获取帧率，这里使用估算
      const estimatedFps = 30 // 可以根据视频格式进行更精确的估算
      setFps(estimatedFps)
      
      // 设置初始帧位置
      if (initialFrame > 0) {
        const initialTime = initialFrame / estimatedFps
        video.currentTime = Math.min(initialTime, video.duration)
      }
    }
  }

  // 时间更新处理
  const handleTimeUpdate = () => {
    const video = videoRef.current
    if (video) {
      const time = video.currentTime
      setCurrentTime(time)
      setCurrentFrame(Math.floor(time * fps))
    }
  }

  // 播放/暂停切换
  const togglePlayPause = () => {
    const video = videoRef.current
    if (video) {
      if (isPlaying) {
        video.pause()
      } else {
        video.play()
      }
      setIsPlaying(!isPlaying)
    }
  }

  // 跳转到指定时间
  const seekToTime = (time) => {
    const video = videoRef.current
    if (video) {
      video.currentTime = Math.max(0, Math.min(time, duration))
    }
  }

  // 逐帧控制
  const stepFrame = (direction) => {
    const frameTime = 1 / fps
    const newTime = currentTime + (direction * frameTime)
    seekToTime(newTime)
  }

  // 进度条拖拽
  const handleProgressChange = (e) => {
    const progress = parseFloat(e.target.value)
    const newTime = (progress / 100) * duration
    seekToTime(newTime)
  }

  // 确认选择当前帧
  const handleConfirmFrame = () => {
    onFrameSelect(currentFrame)
    onClose()
  }

  // 显示控制条
  const showControlsHandler = () => {
    setShowControls(true)
    if (hideControlsTimeout) {
      clearTimeout(hideControlsTimeout)
    }
    // 3秒后自动隐藏控制条（除非正在播放）
    const timeout = setTimeout(() => {
      if (!isPlaying) {
        setShowControls(false)
      }
    }, 3000)
    setHideControlsTimeout(timeout)
  }

  // 隐藏控制条
  const hideControlsHandler = () => {
    if (hideControlsTimeout) {
      clearTimeout(hideControlsTimeout)
    }
    // 延迟隐藏，给用户时间移动到控制条上
    const timeout = setTimeout(() => {
      if (!isPlaying) {
        setShowControls(false)
      }
    }, 1000)
    setHideControlsTimeout(timeout)
  }

  // 清理定时器
  useEffect(() => {
    return () => {
      if (hideControlsTimeout) {
        clearTimeout(hideControlsTimeout)
      }
    }
  }, [hideControlsTimeout])

  // 播放状态改变时的控制条显示逻辑
  useEffect(() => {
    if (isPlaying) {
      setShowControls(true)
      if (hideControlsTimeout) {
        clearTimeout(hideControlsTimeout)
      }
    } else {
      showControlsHandler()
    }
  }, [isPlaying])

  // 格式化时间显示
  const formatTime = (time) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }

  if (!videoFile || !videoUrl) {
    return null
  }

  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0

  return (
    <div className="video-frame-picker-overlay">
      <div className="video-frame-picker-modal">
        <div className="video-frame-picker-header">
          <h3>选择视频帧</h3>
          <button className="close-btn" onClick={onClose}>
            <X className="btn-icon" />
          </button>
        </div>

        <div className="video-frame-picker-content">
          {/* 视频播放器 */}
          <div
            className="video-container"
            onMouseEnter={showControlsHandler}
            onMouseLeave={hideControlsHandler}
            onMouseMove={showControlsHandler}
          >
            <video
              ref={videoRef}
              src={videoUrl}
              onLoadedMetadata={handleLoadedMetadata}
              onTimeUpdate={handleTimeUpdate}
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
              className="video-player"
              onClick={togglePlayPause}
            />

            {/* 悬浮控制条 */}
            {showControls && (
              <div className="video-controls-overlay">
                {/* 进度条 */}
                <div className="progress-container">
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={progressPercentage}
                    onChange={handleProgressChange}
                    className="progress-slider"
                  />
                </div>

                {/* 播放控制 */}
                <div className="playback-controls">
                  <button
                    className="control-btn"
                    onClick={() => stepFrame(-1)}
                    title="上一帧"
                  >
                    <SkipBack className="btn-icon" />
                  </button>

                  <button
                    className="control-btn play-pause-btn"
                    onClick={togglePlayPause}
                    title={isPlaying ? "暂停" : "播放"}
                  >
                    {isPlaying ? <Pause className="btn-icon" /> : <Play className="btn-icon" />}
                  </button>

                  <button
                    className="control-btn"
                    onClick={() => stepFrame(1)}
                    title="下一帧"
                  >
                    <SkipForward className="btn-icon" />
                  </button>
                </div>

                {/* 时间和帧信息 */}
                <div className="time-info">
                  <span className="current-time">{formatTime(currentTime)}</span>
                  <span className="separator">/</span>
                  <span className="total-time">{formatTime(duration)}</span>
                  <span className="frame-info">帧: {currentFrame}</span>
                </div>
              </div>
            )}
          </div>

          {/* 确认按钮 */}
          <div className="action-buttons">
            <button className="btn btn-primary" onClick={handleConfirmFrame}>
              <Check className="btn-icon" />
              确认选择第 {currentFrame} 帧
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default VideoFramePicker
