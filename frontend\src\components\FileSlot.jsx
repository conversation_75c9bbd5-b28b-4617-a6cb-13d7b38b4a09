import React, { useRef } from 'react'
import { Upload, X, File } from 'lucide-react'
import './FileSlot.css'

const FileSlot = ({ 
  file, 
  onFileSelect, 
  onFileRemove, 
  slotIndex, 
  label, 
  accept = "image/*",
  disabled = false 
}) => {
  const fileInputRef = useRef(null)

  const handleFileSelect = (event) => {
    const selectedFile = event.target.files[0]
    if (selectedFile && onFileSelect) {
      onFileSelect(selectedFile, slotIndex)
    }
    // 清空input值，允许重复选择同一文件
    event.target.value = ''
  }

  const handleClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const handleRemove = (e) => {
    e.stopPropagation()
    if (onFileRemove) {
      onFileRemove(slotIndex)
    }
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileType = (file) => {
    if (!file) return ''
    const extension = file.name.split('.').pop()?.toLowerCase()
    return extension ? `.${extension}` : ''
  }

  return (
    <div className="file-slot">
      {label && (
        <div className="file-slot-label">{label}</div>
      )}
      
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileSelect}
        style={{ display: 'none' }}
        disabled={disabled}
      />

      {file ? (
        <div className="file-item" onClick={handleClick}>
          {file.type?.startsWith('image/') && (
            <img 
              src={URL.createObjectURL(file)} 
              alt={file.name}
              onLoad={(e) => URL.revokeObjectURL(e.target.src)}
            />
          )}
          {!file.type?.startsWith('image/') && (
            <div className="file-icon-placeholder">
              <File className="file-icon" />
            </div>
          )}
          <div className="file-name">{file.name}</div>
          <div className="file-meta">
            <span className="file-size">{formatFileSize(file.size)}</span>
            <span className="file-type">{getFileType(file)}</span>
          </div>
          <button 
            className="file-remove"
            onClick={handleRemove}
            disabled={disabled}
            title="移除文件"
          >
            ×
          </button>
        </div>
      ) : (
        <div 
          className={`file-slot-empty ${disabled ? 'disabled' : ''}`}
          onClick={handleClick}
        >
          <div className="add-icon">+</div>
          <div className="add-text">点击选择文件</div>
        </div>
      )}
    </div>
  )
}

export default FileSlot
