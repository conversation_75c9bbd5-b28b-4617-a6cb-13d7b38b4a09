import React from 'react'
import { X } from 'lucide-react'
import FileSlot from './FileSlot'
import './TaskFileGroup.css'

const TaskFileGroup = ({ 
  fileGroup, 
  onUpdate, 
  onRemove, 
  operation,
  disabled = false 
}) => {
  // 获取操作的文件数量要求
  const getRequiredFileCount = (operation) => {
    const twoFileOperations = ['image_fusion', 'image_stitching', 'texture_transfer']
    return twoFileOperations.includes(operation) ? 2 : 1
  }

  // 获取操作的文件槽位标签
  const getSlotLabels = (operation) => {
    switch (operation) {
      case 'image_fusion':
        return ['主图像', '融合图像']
      case 'image_stitching':
        return ['左图像', '右图像']
      case 'texture_transfer':
        return ['目标图像', '纹理源']
      default:
        return ['文件']
    }
  }

  // 更新文件组名称
  const handleNameChange = (e) => {
    const updatedGroup = { ...fileGroup, name: e.target.value }
    onUpdate(updatedGroup)
  }

  // 更新文件
  const handleFileSelect = (file, slotIndex) => {
    const newFiles = [...(fileGroup.files || [])]
    
    // 确保数组有足够的长度
    while (newFiles.length <= slotIndex) {
      newFiles.push(null)
    }
    
    newFiles[slotIndex] = file

    const updatedGroup = { ...fileGroup, files: newFiles }
    onUpdate(updatedGroup)
  }

  // 移除文件
  const handleFileRemove = (slotIndex) => {
    const newFiles = [...(fileGroup.files || [])]
    newFiles[slotIndex] = null

    const updatedGroup = { ...fileGroup, files: newFiles }
    onUpdate(updatedGroup)
  }

  // 检查文件组是否完整
  const isGroupComplete = () => {
    if (!operation) return false
    const requiredCount = getRequiredFileCount(operation)
    const validFiles = (fileGroup.files || []).filter(f => f)
    return validFiles.length >= requiredCount
  }

  const requiredFileCount = getRequiredFileCount(operation)
  const slotLabels = getSlotLabels(operation)
  const isComplete = isGroupComplete()

  return (
    <div className="task-file-group">
      <div className="file-group-header">
        <input
          type="text"
          className="group-name-input"
          value={fileGroup.name || ''}
          onChange={handleNameChange}
          placeholder="文件组名称"
          disabled={disabled}
        />
        <div className="group-status">
          {isComplete ? (
            <span className="status-complete">✓ 已完成</span>
          ) : (
            <span className="status-incomplete">待完成</span>
          )}
        </div>
        <button
          className="remove-group-btn"
          onClick={() => onRemove(fileGroup.id)}
          disabled={disabled}
          title="删除文件组"
        >
          <X className="remove-icon" />
        </button>
      </div>

      <div className="file-slots">
        {Array(requiredFileCount).fill(null).map((_, index) => (
          <FileSlot
            key={`${fileGroup.id}-slot-${index}`}
            file={fileGroup.files?.[index]}
            onFileSelect={handleFileSelect}
            onFileRemove={handleFileRemove}
            slotIndex={index}
            label={slotLabels[index] || `文件 ${index + 1}`}
            accept="image/*"
            disabled={disabled}
          />
        ))}
      </div>
    </div>
  )
}

export default TaskFileGroup
