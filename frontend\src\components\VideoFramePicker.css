/* 视频帧选择器样式 */
.video-frame-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(30, 60, 114, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeIn 0.3s ease-in-out;
}

.video-frame-picker-modal {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.video-frame-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.video-frame-picker-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.close-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  color: white;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

.video-frame-picker-content {
  padding: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: 0; /* 允许内容收缩 */
}

/* 视频容器 */
.video-container {
  position: relative;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
}

.video-player {
  width: 100%;
  height: auto;
  max-height: 400px; /* 恢复原始高度 */
  display: block;
}

/* 悬浮控制条 */
.video-controls-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(30, 60, 114, 0.9));
  backdrop-filter: blur(10px);
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: opacity 0.3s ease;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 控制面板（保留作为备用） */
.video-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: #f8fafc;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

/* 播放控制按钮 */
.playback-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.control-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 8px;
  cursor: pointer;
  color: white;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.control-btn:active {
  transform: translateY(0);
}

.play-pause-btn {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  border: none;
  padding: 10px;
}

.play-pause-btn:hover {
  background: linear-gradient(45deg, #5a6fd8 0%, #6a4190 100%);
}

.btn-icon {
  width: 16px;
  height: 16px;
}

.play-pause-btn .btn-icon {
  width: 18px;
  height: 18px;
}

/* 进度条 */
.progress-container {
  flex: 1;
  padding: 0 8px;
}

.progress-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.2);
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.progress-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.progress-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.progress-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 时间信息 */
.time-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  flex-shrink: 0;
  white-space: nowrap;
}

.separator {
  color: rgba(255, 255, 255, 0.7);
}

.frame-info {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
  margin-left: 8px;
  backdrop-filter: blur(4px);
  font-size: 0.7rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.btn {
  padding: 12px 24px;
  border-radius: 8px;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
}

.btn-primary {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.btn-primary:hover {
  background: linear-gradient(45deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-frame-picker-overlay {
    padding: 8px;
    align-items: flex-start; /* 从顶部开始对齐 */
    padding-top: 20px;
  }

  .video-frame-picker-modal {
    max-width: 100%;
    max-height: 95vh;
    margin: 0;
  }

  .video-frame-picker-header,
  .video-frame-picker-content {
    padding: 12px;
  }

  .video-player {
    max-height: 200px; /* 移动端进一步减小视频高度 */
  }

  .video-controls {
    padding: 12px;
    gap: 12px;
  }

  .playback-controls {
    gap: 8px;
  }

  .control-btn {
    padding: 8px;
  }

  .play-pause-btn {
    padding: 12px;
  }

  .time-info {
    font-size: 0.75rem;
    flex-wrap: wrap;
    justify-content: center;
    gap: 4px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 8px;
    margin-top: 8px;
  }

  .btn {
    width: 100%;
    justify-content: center;
    padding: 12px 16px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .video-frame-picker-overlay {
    padding: 4px;
    padding-top: 10px;
  }

  .video-frame-picker-modal {
    max-height: 98vh;
  }

  .video-frame-picker-header,
  .video-frame-picker-content {
    padding: 8px;
  }

  .video-player {
    max-height: 150px;
  }

  .video-controls {
    padding: 8px;
    gap: 8px;
  }

  .control-btn {
    padding: 6px;
  }

  .play-pause-btn {
    padding: 10px;
  }

  .btn-icon {
    width: 16px;
    height: 16px;
  }

  .play-pause-btn .btn-icon {
    width: 20px;
    height: 20px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
