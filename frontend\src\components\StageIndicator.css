.stage-indicator {
  padding: 16px 0;
}

.stages-container {
  display: flex;
  align-items: flex-start;
  gap: 0;
}

.stage-wrapper {
  display: flex;
  align-items: flex-start;
  flex: 1;
}

.stage-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  flex: 1;
}

.stage-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.stage-content {
  flex: 1;
  min-width: 0;
}

.stage-label {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
  transition: color 0.3s ease;
}

.stage-description {
  font-size: 12px;
  line-height: 1.4;
  transition: color 0.3s ease;
}

.stage-connector {
  width: 40px;
  height: 2px;
  background: var(--border-color);
  margin-top: 16px;
  transition: background-color 0.3s ease;
  flex-shrink: 0;
}

/* 不同状态的样式 */
.stage-item.pending {
  background: transparent;
}

.stage-item.pending .stage-icon {
  background: var(--bg-secondary);
  color: var(--text-tertiary);
  border: 2px solid var(--border-color);
}

.stage-item.pending .stage-label {
  color: var(--text-tertiary);
}

.stage-item.pending .stage-description {
  color: var(--text-quaternary);
}

.stage-item.active {
  background: rgba(59, 130, 246, 0.1);
}

.stage-item.active .stage-icon {
  background: #3b82f6;
  color: white;
  border: 2px solid #3b82f6;
  animation: pulse 2s infinite;
}

.stage-item.active .stage-label {
  color: #3b82f6;
}

.stage-item.active .stage-description {
  color: var(--text-secondary);
}

.stage-item.completed .stage-icon {
  background: #10b981;
  color: white;
  border: 2px solid #10b981;
}

.stage-item.completed .stage-label {
  color: #10b981;
}

.stage-item.completed .stage-description {
  color: var(--text-secondary);
}

.stage-item.error .stage-icon {
  background: #ef4444;
  color: white;
  border: 2px solid #ef4444;
}

.stage-item.error .stage-label {
  color: #ef4444;
}

.stage-item.error .stage-description {
  color: var(--text-secondary);
}

.stage-connector.completed {
  background: #10b981;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stage-item {
    gap: 8px;
    padding: 6px;
  }
  
  .stage-icon {
    width: 28px;
    height: 28px;
  }
  
  .stage-label {
    font-size: 13px;
  }
  
  .stage-description {
    font-size: 11px;
  }
  
  .stage-connector {
    width: 30px;
    margin-top: 14px;
  }
}
