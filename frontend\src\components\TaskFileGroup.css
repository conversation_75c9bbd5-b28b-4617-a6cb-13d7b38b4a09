/* 任务内文件组样式 */
.task-file-group {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.task-file-group:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.file-group-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.group-name-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 6px 10px;
  color: white;
  font-size: 13px;
  font-weight: 500;
  flex: 1;
  min-width: 120px;
  transition: all 0.2s ease;
}

.group-name-input:focus {
  outline: none;
  border-color: rgba(99, 102, 241, 0.5);
  background: rgba(255, 255, 255, 0.15);
}

.group-name-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.group-status {
  font-size: 12px;
  font-weight: 500;
}

.status-complete {
  color: #10b981;
}

.status-incomplete {
  color: #f59e0b;
}

.remove-group-btn {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 4px;
  padding: 4px;
  color: #ef4444;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-group-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.4);
}

.remove-group-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.remove-icon {
  width: 14px;
  height: 14px;
}

.file-slots {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-file-group {
    padding: 10px;
  }

  .file-group-header {
    flex-wrap: wrap;
    gap: 8px;
  }

  .group-name-input {
    min-width: 100px;
    font-size: 12px;
    padding: 5px 8px;
  }

  .file-slots {
    flex-direction: column;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .task-file-group {
    padding: 8px;
    margin-bottom: 8px;
  }

  .file-group-header {
    font-size: 12px;
  }

  .group-name-input {
    font-size: 11px;
    padding: 4px 6px;
  }

  .group-status {
    font-size: 11px;
  }
}
