import React, { useState } from 'react'
import Header from './components/Header'
import BatchProcessor from './components/BatchProcessor'
import LogOutput from './components/LogOutput'
import MessageDisplay from './components/MessageDisplay'
import { ApiProvider } from './contexts/ApiContext'
import { MessageProvider } from './contexts/MessageContext'
import './App.css'

function App() {
  const [logs, setLogs] = useState([])

  // 添加日志
  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev, {
      id: Date.now() + Math.random(), // 添加随机数确保唯一性
      timestamp,
      message,
      type
    }])
  }

  // 清除日志
  const clearLogs = () => {
    setLogs([])
  }

  return (
    <ApiProvider>
      <MessageProvider>
        <div className="app">
          <Header />

          <main className="main-content">
            <div className="container">
              <div className="app-layout">
                {/* 处理器区域 */}
                <div className="processor-section">
                  <BatchProcessor onLog={addLog} />
                </div>

                {/* 日志输出 */}
                <div className="log-section">
                  <LogOutput
                    logs={logs}
                    onClear={clearLogs}
                  />
                </div>
              </div>
            </div>
          </main>

          <MessageDisplay />
        </div>
      </MessageProvider>
    </ApiProvider>
  )
}

export default App
