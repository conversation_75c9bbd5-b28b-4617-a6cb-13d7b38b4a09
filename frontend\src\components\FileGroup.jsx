import React, { useState, useEffect } from 'react'
import { X, Check, AlertCircle } from 'lucide-react'
import FileSlot from './FileSlot'
import './FileGroup.css'

const FileGroup = ({ 
  group, 
  onUpdate, 
  onRemove, 
  operations = {},
  disabled = false 
}) => {
  const [localGroup, setLocalGroup] = useState(group)

  useEffect(() => {
    setLocalGroup(group)
  }, [group])

  // 获取操作的文件数量要求
  const getRequiredFileCount = (operation) => {
    const twoFileOperations = ['image_fusion', 'image_stitching', 'texture_transfer']
    return twoFileOperations.includes(operation) ? 2 : 1
  }

  // 获取操作的文件槽位标签
  const getSlotLabels = (operation) => {
    switch (operation) {
      case 'image_fusion':
        return ['主图像', '融合图像']
      case 'image_stitching':
        return ['左图像', '右图像']
      case 'texture_transfer':
        return ['目标图像', '纹理源']
      default:
        return ['文件']
    }
  }

  // 检查文件组是否完整
  const isGroupComplete = () => {
    if (!localGroup.operation) return false
    const requiredCount = getRequiredFileCount(localGroup.operation)
    const validFiles = localGroup.files.filter(f => f && f.file)
    return validFiles.length >= requiredCount
  }

  // 更新组名
  const handleNameChange = (e) => {
    const updatedGroup = { ...localGroup, name: e.target.value }
    setLocalGroup(updatedGroup)
    onUpdate(updatedGroup)
  }

  // 更新操作
  const handleOperationChange = (e) => {
    const operation = e.target.value
    const requiredCount = getRequiredFileCount(operation)
    
    // 重置文件数组以匹配新操作的要求
    const newFiles = Array(requiredCount).fill(null).map((_, index) => 
      localGroup.files[index] || { id: `${localGroup.id}-file-${index}`, file: null, slot: index }
    )

    const updatedGroup = { 
      ...localGroup, 
      operation,
      requiredFileCount: requiredCount,
      files: newFiles,
      parameters: {} // 重置参数
    }
    setLocalGroup(updatedGroup)
    onUpdate(updatedGroup)
  }

  // 更新文件
  const handleFileSelect = (file, slotIndex) => {
    const newFiles = [...localGroup.files]
    newFiles[slotIndex] = {
      id: `${localGroup.id}-file-${slotIndex}`,
      file: file,
      slot: slotIndex
    }

    const updatedGroup = { ...localGroup, files: newFiles }
    setLocalGroup(updatedGroup)
    onUpdate(updatedGroup)
  }

  // 移除文件
  const handleFileRemove = (slotIndex) => {
    const newFiles = [...localGroup.files]
    newFiles[slotIndex] = { 
      id: `${localGroup.id}-file-${slotIndex}`, 
      file: null, 
      slot: slotIndex 
    }

    const updatedGroup = { ...localGroup, files: newFiles }
    setLocalGroup(updatedGroup)
    onUpdate(updatedGroup)
  }

  // 获取操作列表
  const getOperationsList = () => {
    return operations.image || []
  }

  const requiredFileCount = getRequiredFileCount(localGroup.operation)
  const slotLabels = getSlotLabels(localGroup.operation)
  const isComplete = isGroupComplete()

  return (
    <div className="file-group">
      <div className="file-group-header">
        <div className="group-info">
          <input
            type="text"
            className="group-name-input"
            value={localGroup.name}
            onChange={handleNameChange}
            placeholder="文件组名称"
            disabled={disabled}
          />
          <div className="group-status">
            {isComplete ? (
              <div className="status-complete">
                <Check className="status-icon" />
                <span>已完成</span>
              </div>
            ) : (
              <div className="status-incomplete">
                <AlertCircle className="status-icon" />
                <span>待完成</span>
              </div>
            )}
          </div>
        </div>
        <button
          className="remove-group-btn"
          onClick={() => onRemove(localGroup.id)}
          disabled={disabled}
          title="删除文件组"
        >
          <X className="remove-icon" />
        </button>
      </div>

      <div className="group-config">
        <div className="operation-selector">
          <label className="config-label">操作类型:</label>
          <select
            className="operation-select"
            value={localGroup.operation}
            onChange={handleOperationChange}
            disabled={disabled}
          >
            <option value="">选择操作</option>
            {getOperationsList().map(op => (
              <option key={op.id} value={op.id}>
                {op.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {localGroup.operation && (
        <div className="file-slots">
          {Array(requiredFileCount).fill(null).map((_, index) => (
            <FileSlot
              key={`${localGroup.id}-slot-${index}`}
              file={localGroup.files[index]?.file}
              onFileSelect={handleFileSelect}
              onFileRemove={handleFileRemove}
              slotIndex={index}
              label={slotLabels[index] || `文件 ${index + 1}`}
              accept="image/*"
              disabled={disabled}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export default FileGroup
