import React, { useState, useEffect } from 'react'
import { Plus, X, Play, Folder, FileText, Settings } from 'lucide-react'
import { useApi } from '../contexts/ApiContext'
import { useMessage } from '../contexts/MessageContext'
import FilePreview from './FilePreview'
import BatchResults from './BatchResults'
import TaskFileGroup from './TaskFileGroup'
import VideoFramePicker from './VideoFramePicker'
import './BatchProcessor.css'

const BatchProcessor = ({ onLog }) => {
  const { apiBase } = useApi()
  const { showSuccess, showError } = useMessage()
  const [batchTasks, setBatchTasks] = useState([])
  const [operations, setOperations] = useState({})
  const [isProcessing, setIsProcessing] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const [completedTaskIds, setCompletedTaskIds] = useState([])
  const [showVideoFramePicker, setShowVideoFramePicker] = useState(false)
  const [currentVideoPickerTask, setCurrentVideoPickerTask] = useState(null)


  // 判断操作是否需要多个文件
  const isMultiFileOperation = (operation) => {
    const multiFileOps = ['image_fusion', 'image_stitching', 'texture_transfer']
    return multiFileOps.includes(operation)
  }

  // 判断操作是否限制单个文件
  const isSingleFileOnlyOperation = (operation) => {
    const singleFileOps = ['extract_frame']
    return singleFileOps.includes(operation)
  }



  // 获取操作需要的文件数量
  const getRequiredFileCount = (operation) => {
    return isMultiFileOperation(operation) ? 2 : 1
  }

  // 从任务中提取所有实际的文件
  const extractFilesFromTask = (task) => {
    const allFiles = []
    task.files.forEach(item => {
      if (item && item.type === 'fileGroup') {
        // 文件组：提取内部文件
        if (item.files && Array.isArray(item.files)) {
          item.files.forEach(file => {
            if (file) allFiles.push(file)
          })
        }
      } else if (item) {
        // 普通文件
        allFiles.push(item)
      }
    })
    return allFiles
  }

  // 获取操作列表
  useEffect(() => {
    const fetchOperations = async () => {
      try {
        const response = await fetch(`${apiBase}/operations`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          mode: 'cors',
          cache: 'no-cache'
        })
        if (response.ok) {
          const data = await response.json()
          setOperations(data.operations || {})
          onLog('操作列表加载成功', 'success')
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
      } catch (error) {
        onLog(`获取操作列表失败: ${error.message}`, 'error')
      }
    }

    fetchOperations()
  }, [apiBase])

  // 添加批处理任务
  const addBatchTask = () => {
    const newTask = {
      id: Date.now(),
      name: `任务 ${batchTasks.length + 1}`,
      fileType: 'image',
      files: [],
      operation: '',
      parameters: {}
    }
    setBatchTasks([...batchTasks, newTask])
    onLog(`添加新任务: ${newTask.name}`, 'info')
  }

  // 删除批处理任务
  const removeBatchTask = (taskId) => {
    const task = batchTasks.find(t => t.id === taskId)
    setBatchTasks(batchTasks.filter(task => task.id !== taskId))
    onLog(`删除任务: ${task?.name}`, 'info')
  }

  // 更新任务
  const updateTask = (taskId, updates) => {
    setBatchTasks(batchTasks.map(task => 
      task.id === taskId ? { ...task, ...updates } : task
    ))
  }

  // 添加文件到任务
  const addFilesToTask = (taskId, files) => {
    const task = batchTasks.find(t => t.id === taskId)
    if (task) {
      const newFiles = [...task.files, ...Array.from(files)]
      updateTask(taskId, { files: newFiles })
      onLog(`任务 ${task.name} 添加了 ${files.length} 个文件`, 'info')
    }
  }

  // 从任务中移除文件
  const removeFileFromTask = (taskId, fileIndex) => {
    const task = batchTasks.find(t => t.id === taskId)
    if (task) {
      const removedFile = task.files[fileIndex]
      const newFiles = task.files.filter((_, index) => index !== fileIndex)
      updateTask(taskId, { files: newFiles })
      onLog(`从任务 ${task.name} 移除文件: ${removedFile.name}`, 'info')
    }
  }

  // 更新任务参数
  const updateTaskParameter = (taskId, paramName, value) => {
    const task = batchTasks.find(t => t.id === taskId)
    if (task) {
      const newParameters = { ...task.parameters, [paramName]: value }
      updateTask(taskId, { parameters: newParameters })
    }
  }

  // 获取参数的配置信息
  const getParameterConfig = (paramName) => {
    const configs = {
      // 纹理迁移参数
      'block_size': {
        type: 'slider',
        min: 8,
        max: 50,
        step: 1,
        default: 27,
        label: '块大小',
        description: '纹理块的大小，较小的值产生更细致的纹理'
      },
      'alpha_start': {
        type: 'slider',
        min: 0.0,
        max: 1.0,
        step: 0.1,
        default: 0.1,
        label: '起始权重',
        description: '初始层的纹理混合权重'
      },
      'alpha_end': {
        type: 'slider',
        min: 0.0,
        max: 1.0,
        step: 0.1,
        default: 0.9,
        label: '结束权重',
        description: '最终层的纹理混合权重'
      },
      // 图像锐化参数
      'intensity': {
        type: 'slider',
        min: 0.1,
        max: 3.0,
        step: 0.1,
        default: 1.0,
        label: '锐化强度',
        description: '锐化效果的强度'
      },
      // 伽马校正参数
      'gamma': {
        type: 'slider',
        min: 0.1,
        max: 3.0,
        step: 0.1,
        default: 1.0,
        label: '伽马值',
        description: '伽马校正的强度'
      },
      // Canny边缘检测参数
      'low_threshold': {
        type: 'slider',
        min: 10,
        max: 200,
        step: 10,
        default: 50,
        label: '低阈值',
        description: '边缘检测的低阈值'
      },

      // 图像融合参数
      'weight1': {
        type: 'slider',
        min: 0.0,
        max: 1.0,
        step: 0.1,
        default: 0.5,
        label: '第一图像权重',
        description: '第一张图像在融合中的权重'
      },
      'weight2': {
        type: 'slider',
        min: 0.0,
        max: 1.0,
        step: 0.1,
        default: 0.5,
        label: '第二图像权重',
        description: '第二张图像在融合中的权重'
      },

      // 视频处理参数
      // 视频缩放参数 - 完全重新设计
      'scale_mode': {
        type: 'select',
        options: [
          { value: 'ratio', label: '按比例缩放' },
          { value: 'custom', label: '指定尺寸' }
        ],
        default: 'ratio',
        label: '缩放模式',
        description: '选择视频缩放的方式'
      },
      'scale_ratio': {
        type: 'slider',
        min: 0.1,
        max: 3.0,
        step: 0.1,
        default: 1.0,
        label: '缩放比例',
        description: '视频缩放的比例（1.0为原始大小）',
        dependsOn: 'scale_mode',
        showWhen: 'ratio'
      },
      'width': {
        type: 'input',
        inputType: 'number',
        min: 320,
        max: 1920,
        default: 640,
        label: '目标宽度',
        description: '输出视频的宽度（像素）',
        dependsOn: 'scale_mode',
        showWhen: 'custom'
      },
      'height': {
        type: 'input',
        inputType: 'number',
        min: 240,
        max: 1080,
        default: 480,
        label: '目标高度',
        description: '输出视频的高度（像素）',
        dependsOn: 'scale_mode',
        showWhen: 'custom'
      },

      // 视频二值化参数
      'threshold': {
        type: 'slider',
        min: 0,
        max: 255,
        step: 1,
        default: 127,
        label: '二值化阈值',
        description: '二值化处理的阈值'
      },
      'max_value': {
        type: 'slider',
        min: 0,
        max: 255,
        step: 1,
        default: 255,
        label: '最大值',
        description: '二值化处理的最大值'
      },

      // 视频滤波参数
      'kernel_size': {
        type: 'slider',
        min: 1,
        max: 51,
        step: 2,
        default: 5,
        label: '滤波核大小',
        description: '滤波器核的大小'
      },
      'sigma': {
        type: 'slider',
        min: 0.1,
        max: 10.0,
        step: 0.1,
        default: 1.0,
        label: 'Sigma值',
        description: '高斯滤波的标准差'
      },


      'high_threshold': {
        type: 'slider',
        min: 0,
        max: 255,
        step: 1,
        default: 150,
        label: '高阈值',
        description: 'Canny边缘检测的高阈值'
      },

      // 视频变换参数
      'angle': {
        type: 'slider',
        min: -360,
        max: 360,
        step: 1,
        default: 90,
        label: '旋转角度',
        description: '自定义旋转的角度（度）',
        dependsOn: 'transform_type',
        showWhen: 'rotate_custom'
      },





      // 下拉选择参数
      'threshold_type': {
        type: 'select',
        options: [
          { value: 'binary', label: '二值化' },
          { value: 'binary_inv', label: '反二值化' },
          { value: 'trunc', label: '截断' },
          { value: 'tozero', label: '阈值化为零' },
          { value: 'tozero_inv', label: '反阈值化为零' }
        ],
        default: 'binary',
        label: '阈值类型',
        description: '二值化处理的类型'
      },
      'filter_type': {
        type: 'select',
        options: [
          { value: 'gaussian', label: '高斯滤波' },
          { value: 'median', label: '中值滤波' },
          { value: 'bilateral', label: '双边滤波' },
          { value: 'sharpen', label: '锐化滤波' }
        ],
        default: 'gaussian',
        label: '滤波类型',
        description: '视频滤波的类型'
      },
      'edge_type': {
        type: 'select',
        options: [
          { value: 'canny', label: 'Canny边缘检测' },
          { value: 'sobel', label: 'Sobel边缘检测' },
          { value: 'laplacian', label: '拉普拉斯边缘检测' }
        ],
        default: 'canny',
        label: '边缘检测类型',
        description: '边缘检测算法的类型'
      },
      'transform_type': {
        type: 'select',
        options: [
          { value: 'rotate_90', label: '顺时针旋转90°' },
          { value: 'rotate_180', label: '旋转180°' },
          { value: 'rotate_270', label: '逆时针旋转90°' },
          { value: 'flip_horizontal', label: '水平翻转' },
          { value: 'flip_vertical', label: '垂直翻转' },
          { value: 'flip_both', label: '水平垂直翻转' },
          { value: 'rotate_custom', label: '自定义角度旋转' }
        ],
        default: 'rotate_90',
        label: '变换类型',
        description: '视频变换的类型'
      },


      // 提取帧参数 - 特殊处理
      'frame_number': {
        type: 'video_frame_picker',
        default: 0,
        label: '选择帧',
        description: '通过视频播放器选择要提取的帧'
      },

      // 智能美颜参数
      'slimming_strength': {
        type: 'slider',
        min: 0.0,
        max: 1.0,
        step: 0.1,
        default: 0.3,
        label: '瘦脸强度',
        description: '瘦脸效果的强度，0为无效果，1为最强效果'
      },
      'smoothing_strength': {
        type: 'slider',
        min: 0.0,
        max: 1.0,
        step: 0.1,
        default: 0.5,
        label: '磨皮强度',
        description: '磨皮效果的强度，0为无效果，1为最强效果'
      },

      // 图像拼接参数
      'mode': {
        type: 'select',
        options: [
          { value: 'panorama', label: '全景拼接' },
          { value: 'scans', label: '扫描拼接' }
        ],
        default: 'panorama',
        label: '拼接模式',
        description: '选择图像拼接的模式'
      }
    }

    return configs[paramName] || {
      type: 'input',
      default: '',
      label: paramName,
      description: `请输入${paramName}参数`
    }
  }

  // 渲染参数控制组件
  const renderParameterControl = (task, paramName) => {
    const config = getParameterConfig(paramName)
    const currentValue = task.parameters[paramName] !== undefined ? task.parameters[paramName] : config.default

    if (config.type === 'slider') {
      return (
        <div className="parameter-slider-container">
          <div className="slider-wrapper">
            <input
              type="range"
              className="parameter-slider"
              min={config.min}
              max={config.max}
              step={config.step}
              value={currentValue}
              onChange={(e) => updateTaskParameter(task.id, paramName, parseFloat(e.target.value))}
              disabled={isProcessing}
            />
            <div className="slider-value">{currentValue}</div>
          </div>
          <div className="slider-range">
            <span>{config.min}</span>
            <span>{config.max}</span>
          </div>
        </div>
      )
    } else if (config.type === 'select') {
      return (
        <div className="parameter-select-container">
          <select
            className="parameter-select"
            value={currentValue}
            onChange={(e) => updateTaskParameter(task.id, paramName, e.target.value)}
            disabled={isProcessing}
          >
            {config.options.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      )
    } else if (config.type === 'checkbox') {
      return (
        <div className="parameter-checkbox-container">
          <label className="parameter-checkbox-label">
            <input
              type="checkbox"
              className="parameter-checkbox"
              checked={currentValue}
              onChange={(e) => updateTaskParameter(task.id, paramName, e.target.checked)}
              disabled={isProcessing}
            />
            <span className="checkbox-text">{config.label}</span>
          </label>
        </div>
      )
    } else if (config.type === 'input') {
      return (
        <div className="parameter-input-container">
          <input
            type={config.inputType || 'text'}
            className="parameter-input"
            value={currentValue}
            min={config.min}
            max={config.max}
            onChange={(e) => {
              const value = config.inputType === 'number' ? parseFloat(e.target.value) || 0 : e.target.value
              updateTaskParameter(task.id, paramName, value)
            }}
            placeholder={`输入${paramName}`}
            disabled={isProcessing}
          />
        </div>
      )
    } else if (config.type === 'video_frame_picker') {
      return (
        <div className="video-frame-picker">
          <div className="frame-picker-info">
            <span>当前帧: {currentValue}</span>
            <button
              className="btn btn-secondary"
              onClick={() => {
                setCurrentVideoPickerTask(task)
                setShowVideoFramePicker(true)
              }}
              disabled={isProcessing || task.files.length === 0}
            >
              选择帧
            </button>
          </div>
        </div>
      )
    } else {
      // 默认输入框
      return (
        <input
          type="text"
          className="input parameter-input"
          value={currentValue}
          onChange={(e) => updateTaskParameter(task.id, paramName, e.target.value)}
          placeholder={`输入${paramName}`}
          disabled={isProcessing}
        />
      )
    }
  }

  // 获取当前任务的操作列表
  const getTaskOperations = (fileType) => {
    return operations[fileType] || []
  }

  // 添加文件组到任务
  const addFileGroupToTask = (taskId) => {
    const task = batchTasks.find(t => t.id === taskId)
    if (task) {
      const newFileGroup = {
        id: `group-${Date.now()}`,
        type: 'fileGroup',
        name: `文件组 ${(task.files.filter(f => f.type === 'fileGroup').length + 1)}`,
        files: [],
        requiredCount: getRequiredFileCount(task.operation)
      }
      const newFiles = [...task.files, newFileGroup]
      updateTask(taskId, { files: newFiles })
      onLog(`任务 ${task.name} 添加了文件组: ${newFileGroup.name}`, 'info')
    }
  }

  // 更新文件组
  const updateFileGroup = (taskId, groupId, updatedGroup) => {
    const task = batchTasks.find(t => t.id === taskId)
    if (task) {
      const newFiles = task.files.map(item =>
        item.type === 'fileGroup' && item.id === groupId ? updatedGroup : item
      )
      updateTask(taskId, { files: newFiles })
    }
  }

  // 删除文件组
  const removeFileGroup = (taskId, groupId) => {
    const task = batchTasks.find(t => t.id === taskId)
    if (task) {
      const group = task.files.find(f => f.type === 'fileGroup' && f.id === groupId)
      const newFiles = task.files.filter(item => !(item.type === 'fileGroup' && item.id === groupId))
      updateTask(taskId, { files: newFiles })
      if (group) {
        onLog(`从任务 ${task.name} 删除文件组: ${group.name}`, 'info')
      }
    }
  }

  // 获取当前操作的参数列表
  const getOperationParameters = (fileType, operationId) => {
    const ops = getTaskOperations(fileType)
    const op = ops.find(o => o.id === operationId)
    return op?.parameters || []
  }

  // 获取操作的默认参数
  const getDefaultParametersForOperation = (operation) => {
    const defaultParams = {}

    // 根据操作类型设置默认参数
    switch (operation) {
      case 'resize':
        defaultParams.width = 640
        defaultParams.height = 480
        defaultParams.scale_mode = 'ratio'
        defaultParams.scale_ratio = 1.0
        break
      case 'blur':
        defaultParams.filter_type = 'gaussian'
        defaultParams.kernel_size = 5
        defaultParams.sigma = 1.0
        break
      case 'edge_detection':
        defaultParams.low_threshold = 50
        defaultParams.high_threshold = 150
        defaultParams.edge_type = 'canny'
        break
      case 'binary':
        defaultParams.threshold = 127
        defaultParams.max_value = 255
        defaultParams.threshold_type = 'binary'
        break
      case 'transform':
        defaultParams.transform_type = 'rotate_90'
        defaultParams.angle = 90
        break

      case 'extract_frame':
        defaultParams.frame_number = 0
        break
      case 'beauty_enhancement':
        defaultParams.slimming_strength = 0.3
        defaultParams.smoothing_strength = 0.5
        break
      case 'image_stitching':
        defaultParams.mode = 'panorama'
        break
      default:
        break
    }

    return defaultParams
  }

  // 开始批处理
  const startBatchProcess = async () => {
    if (batchTasks.length === 0) {
      showError('没有批处理任务')
      return
    }

    const validTasks = batchTasks.filter(task => task.files.length > 0 && task.operation)
    if (validTasks.length === 0) {
      showError('没有有效的批处理任务（需要文件和操作）')
      return
    }

    setIsProcessing(true)
    onLog(`开始批处理，共 ${validTasks.length} 个任务`, 'info')

    const taskIds = []

    try {
      // 使用后端批处理API - 先上传文件，再批处理
      for (const task of validTasks) {
        onLog(`处理任务: ${task.name}`, 'info')

        try {
          // 1. 先上传所有文件
          // 1. 提取所有实际的文件对象（处理文件组和单个文件）
          const allFiles = extractFilesFromTask(task)

          // 过滤出有效的文件对象
          const validFiles = allFiles.filter(file =>
            file &&
            file instanceof File &&
            file.size > 0 &&
            file.name
          )

          if (validFiles.length === 0) {
            throw new Error('没有有效的文件可以上传')
          }

          onLog(`上传文件: ${validFiles.length} 个文件`, 'info')
          const formData = new FormData()
          validFiles.forEach(file => {
            formData.append('files', file)
          })
          // 添加文件类型参数，让后端知道这是视频还是图像文件
          formData.append('file_type', task.fileType)

          const uploadResponse = await fetch(`${apiBase}/upload`, {
            method: 'POST',
            body: formData,
            mode: 'cors'
          })

          if (!uploadResponse.ok) {
            throw new Error(`文件上传失败: HTTP ${uploadResponse.status}`)
          }

          const uploadResult = await uploadResponse.json()
          const uploadedFiles = uploadResult.files || []

          if (uploadedFiles.length === 0) {
            throw new Error('没有文件上传成功')
          }

          onLog(`✅ 文件上传成功: ${uploadedFiles.length} 个文件`, 'success')

          // 2. 提交批处理任务
          // 发送文件信息，让后端自己构造正确的文件路径
          const filesWithPath = uploadedFiles.map(file => ({
            ...file,
            // 移除file_path字段，让后端根据stored_filename构造正确路径
          }))

          const batchData = {
            files: filesWithPath,
            operation: task.operation,
            parameters: task.parameters,
            task_name: task.name,
            original_files: validFiles.map(file => file.name)
          }

          const batchResponse = await fetch(`${apiBase}/process`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(batchData),
            mode: 'cors'
          })

          if (batchResponse.ok) {
            const batchResult = await batchResponse.json()
            console.log('🔍 批处理响应:', batchResult)
            const batchTaskIds = batchResult.task_ids?.map(t => t.task_id) || []
            console.log('🔍 提取的任务IDs:', batchTaskIds)

            if (batchTaskIds.length > 0) {
              taskIds.push(...batchTaskIds)
              console.log('🔍 累积的任务IDs:', taskIds)

              // 存储任务元数据到localStorage
              // 为每个任务ID分配对应的文件组文件
              const taskMetadata = batchResult.task_ids?.map((taskInfo, index) => {
                let originalFiles = []

                if (task.files && task.files.length > 0) {
                  // 检查是否有文件组
                  const fileGroups = task.files.filter(item => item && item.type === 'fileGroup')

                  if (fileGroups.length > 0) {
                    // 有文件组：每个任务对应一个文件组
                    const fileGroup = fileGroups[index]
                    if (fileGroup && fileGroup.files) {
                      originalFiles = fileGroup.files
                        .filter(file => file && file.name)
                        .map(file => file.name)
                    }
                  } else {
                    // 没有文件组：使用后端返回的文件名或所有文件
                    if (taskInfo.filename) {
                      originalFiles = [taskInfo.filename]
                    } else {
                      originalFiles = validFiles.map(file => file.name)
                    }
                  }
                }

                return {
                  task_id: taskInfo.task_id,
                  task_name: task.name,
                  original_files: originalFiles.filter(Boolean)
                }
              }) || []

              const existingMetadata = JSON.parse(localStorage.getItem('taskMetadata') || '{}')
              taskMetadata.forEach(meta => {
                existingMetadata[meta.task_id] = meta
              })
              localStorage.setItem('taskMetadata', JSON.stringify(existingMetadata))

              onLog(`✅ 任务 ${task.name} 提交成功，${batchTaskIds.length} 个子任务`, 'success')
            } else {
              throw new Error('批处理任务创建失败')
            }
          } else {
            throw new Error(`批处理提交失败: HTTP ${batchResponse.status}`)
          }
        } catch (error) {
          onLog(`❌ 任务 ${task.name} 处理失败: ${error.message}`, 'error')
        }
      }

      if (taskIds.length > 0) {
        console.log('🔍 设置完成的任务IDs:', taskIds)
        setCompletedTaskIds(taskIds)
        setShowResults(true)
        showSuccess(`批处理任务已提交，共 ${taskIds.length} 个任务`)
        onLog(`批处理任务已提交，正在处理中...`, 'success')
      } else {
        showError('没有任务成功提交')
      }
    } catch (error) {
      showError(`批处理失败: ${error.message}`)
      onLog(`批处理失败: ${error.message}`, 'error')
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <>
      <div className="batch-processor">
        <div className="card">
        <div className="batch-header">
          <h2 className="section-title">批处理任务</h2>
          <button
            className="btn btn-primary"
            onClick={addBatchTask}
            disabled={isProcessing}
          >
            <Plus className="btn-icon" />
            添加任务
          </button>
        </div>

        {batchTasks.length === 0 ? (
          <div className="empty-state">
            <Folder className="empty-icon" />
            <p>暂无批处理任务</p>
            <p className="empty-description">点击"添加任务"开始创建批处理任务</p>
          </div>
        ) : (
          <div className="batch-tasks">
            {batchTasks.map(task => (
              <div key={task.id} className="batch-task">
                <div className="task-header">
                  <input
                    type="text"
                    className="input task-name"
                    value={task.name}
                    onChange={(e) => updateTask(task.id, { name: e.target.value })}
                    placeholder="任务名称"
                    disabled={isProcessing}
                  />
                  <button
                    className="btn-icon-only remove-task"
                    onClick={() => removeBatchTask(task.id)}
                    disabled={isProcessing}
                  >
                    <X className="btn-icon" />
                  </button>
                </div>

                <div className="task-content">
                  {/* 文件类型选择 */}
                  <div className="task-row">
                    <label className="task-label">文件类型:</label>
                    <select
                      className="select"
                      value={task.fileType}
                      onChange={(e) => updateTask(task.id, {
                        fileType: e.target.value,
                        operation: '',
                        parameters: {},
                        files: []  // 清空文件列表
                      })}
                      disabled={isProcessing}
                    >
                      <option value="image">图片</option>
                      <option value="video">视频</option>
                    </select>
                  </div>

                  {/* 操作选择 */}
                  <div className="task-row">
                    <label className="task-label">操作:</label>
                    <select
                      className="select"
                      value={task.operation}
                      onChange={(e) => {
                        const operation = e.target.value
                        const defaultParams = getDefaultParametersForOperation(operation)
                        updateTask(task.id, {
                          operation: operation,
                          parameters: defaultParams
                        })
                      }}
                      disabled={isProcessing}
                    >
                      <option value="">选择操作</option>
                      {getTaskOperations(task.fileType).map(op => (
                        <option key={op.id} value={op.id}>
                          {op.name}
                        </option>
                      ))}
                    </select>
                  </div>



                  {/* 帧提取操作音频说明 */}
                  {task.fileType === 'video' && task.operation === 'extract_frame' && (
                    <div className="audio-info" style={{
                      backgroundColor: '#f8f9fa',
                      border: '1px solid #dee2e6',
                      borderRadius: '4px',
                      padding: '8px 12px',
                      margin: '8px 0',
                      fontSize: '14px',
                      color: '#6c757d'
                    }}>
                      <span style={{ marginRight: '8px' }}>🖼️</span>
                      <span>
                        帧提取：此操作提取单帧图像，不涉及音频处理。
                      </span>
                    </div>
                  )}

                  {/* 文件选择 */}
                  {task.operation && (
                    <div className="file-section">
                      {/* 标题和添加文件按钮在同一行 */}
                      <div className="file-section-header">
                        <div className="file-section-title">
                          <Folder className="param-icon" />
                          <span>文件列表</span>
                        </div>
                        <div className="file-actions">
                        {task.operation && isMultiFileOperation(task.operation) ? (
                          // 多文件操作：显示添加文件组按钮
                          <button
                            className="btn btn-secondary"
                            onClick={() => addFileGroupToTask(task.id)}
                            disabled={isProcessing || !task.operation}
                          >
                            <Plus className="btn-icon" />
                            添加文件组
                          </button>
                        ) : task.operation === 'extract_frame' ? (
                          // 提取帧操作：只能选择单个视频文件
                          <>
                            {task.files.length === 0 ? (
                              <>
                                <input
                                  type="file"
                                  multiple={false}
                                  accept="video/*"
                                  onChange={(e) => {
                                    if (e.target.files.length > 0) {
                                      // 清空现有文件，只保留新选择的文件
                                      updateTask(task.id, { files: [] })
                                      addFilesToTask(task.id, [e.target.files[0]])
                                    }
                                  }}
                                  className="file-input-hidden"
                                  id={`file-input-${task.id}`}
                                  disabled={isProcessing || !task.operation}
                                />
                                <label
                                  htmlFor={`file-input-${task.id}`}
                                  className={`file-input-label ${(!task.operation || isProcessing) ? 'disabled' : ''}`}
                                >
                                  <Folder className="btn-icon" />
                                  选择视频文件
                                </label>
                              </>
                            ) : (
                              <div className="single-file-selected">
                                <span>已选择视频文件（提取帧只支持单个文件）</span>
                              </div>
                            )}
                            <div className="file-limit-info">
                              <small>提取帧操作只能选择一个视频文件</small>
                            </div>
                          </>
                        ) : (
                          // 普通操作：显示传统的文件选择
                          <>
                            <input
                              type="file"
                              multiple={!isSingleFileOnlyOperation(task.operation)}
                              accept={task.fileType === 'image' ? 'image/*' : 'video/*'}
                              onChange={(e) => addFilesToTask(task.id, e.target.files)}
                              className="file-input-hidden"
                              id={`file-input-${task.id}`}
                              disabled={isProcessing || !task.operation}
                            />
                            <label
                              htmlFor={`file-input-${task.id}`}
                              className={`file-input-label ${(!task.operation || isProcessing) ? 'disabled' : ''}`}
                            >
                              <Folder className="btn-icon" />
                              选择文件
                            </label>
                          </>
                        )}
                        </div>
                      </div>

                      {/* 文件列表显示部分 */}
                      {task.files.length > 0 && (
                        <div className="file-list">
                          {task.files.map((item, index) => (
                            item.type === 'fileGroup' ? (
                              // 显示文件组
                              <TaskFileGroup
                                key={item.id}
                                fileGroup={item}
                                onUpdate={(updatedGroup) => updateFileGroup(task.id, item.id, updatedGroup)}
                                onRemove={(groupId) => removeFileGroup(task.id, groupId)}
                                operation={task.operation}
                                disabled={isProcessing}
                              />
                            ) : (
                              // 显示普通文件
                              <FilePreview
                                key={index}
                                file={item}
                                onRemove={() => removeFileFromTask(task.id, index)}
                                showPreview={task.fileType === 'image'}
                              />
                            )
                          ))}
                        </div>
                      )}
                    </div>
                  )}

                  {/* 参数设置 */}
                  {task.operation && (
                    <div className="parameters-section">
                      <div className="parameters-header">
                        <Settings className="param-icon" />
                        <span>参数设置</span>
                      </div>
                      <div className="parameters-grid">
                        {getOperationParameters(task.fileType, task.operation).length > 0 ? (
                          getOperationParameters(task.fileType, task.operation).map(param => {
                            const config = getParameterConfig(param)

                            // 检查条件显示 - 在这里检查而不是在renderParameterControl中
                            if (config.dependsOn && config.showWhen) {
                              const dependentValue = task.parameters[config.dependsOn] || getParameterConfig(config.dependsOn).default
                              if (dependentValue !== config.showWhen) {
                                return null
                              }
                            }

                            return (
                              <div key={param} className="parameter-item">
                                <label className="parameter-label">
                                  {config.label || param}:
                                </label>
                                {renderParameterControl(task, param)}
                                {config.description && (
                                  <div className="parameter-description">{config.description}</div>
                                )}
                              </div>
                            )
                          }).filter(Boolean)
                        ) : (
                          <div className="no-parameters">
                            <span className="no-parameters-text">此操作无需额外参数</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {batchTasks.length > 0 && (
          <div className="batch-actions">
            <button
              className="btn btn-success"
              onClick={startBatchProcess}
              disabled={isProcessing || batchTasks.every(task => task.files.length === 0 || !task.operation)}
            >
              <Play className="btn-icon" />
              {isProcessing ? '处理中...' : '开始批处理'}
            </button>
          </div>
        )}
        </div>
      </div>

      {/* 视频帧选择器模态框 */}
      {showVideoFramePicker && currentVideoPickerTask && (
        <VideoFramePicker
          videoFile={currentVideoPickerTask.files[0]}
          onFrameSelect={(frameNumber) => {
            updateTaskParameter(currentVideoPickerTask.id, 'frame_number', frameNumber)
            setShowVideoFramePicker(false)
            setCurrentVideoPickerTask(null)
          }}
          onClose={() => {
            setShowVideoFramePicker(false)
            setCurrentVideoPickerTask(null)
          }}
          initialFrame={currentVideoPickerTask.parameters?.frame_number || 0}
        />
      )}

      {/* 批处理结果模态框 */}
      {showResults && (
        <BatchResults
          taskIds={completedTaskIds}
          onClose={() => setShowResults(false)}
        />
      )}
    </>
  )
}

export default BatchProcessor
