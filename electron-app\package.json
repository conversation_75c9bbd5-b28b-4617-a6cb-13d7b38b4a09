{"name": "file-processor-desktop", "version": "1.0.0", "description": "文件处理器桌面应用", "main": "main.js", "scripts": {"start": "electron .", "dev": "NODE_ENV=development electron .", "build": "electron-builder", "dist": "electron-builder --publish=never", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["file-processor", "image-processing", "video-processing", "desktop-app"], "author": "File Processor Team", "license": "MIT", "homepage": ".", "build": {"appId": "com.fileprocessor.desktop", "productName": "文件处理器", "directories": {"output": "release"}, "files": ["main.js", "preload.js", {"from": "../frontend/dist", "to": "frontend-dist", "filter": ["**/*"]}], "extraResources": [{"from": "../backend/dist/FileProcessorBackendSync", "to": "backend/FileProcessorBackendSync"}], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "devDependencies": {"@yarnpkg/sdks": "^3.2.2", "electron": "^37.2.4", "electron-builder": "^26.0.12"}, "volta": {"node": "22.18.0", "yarn": "4.9.2"}}