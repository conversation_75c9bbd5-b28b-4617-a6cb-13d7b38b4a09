"""
Tasks package initialization - 重构后的任务注册
"""

def register_tasks(celery_app):
    """注册所有任务到celery应用"""
    
    # 图像处理任务
    from .image_sharpen import sharpen_image_task
    from .image_gamma_correction import gamma_correction_task
    from .image_grayscale import grayscale_image_task
    from .image_edge_detection import canny_edge_detection_task
    from .image_fusion import image_fusion_task
    
    # 高级图像处理任务
    from .image_stitching import image_stitching_task
    from .texture_transfer import texture_transfer_task

    # 美颜处理任务
    from .beauty_processing import beauty_enhancement_task
    
    # 视频处理任务（统一入口）
    from .video_tasks import process_video_task

    # 简单测试任务
    from .test_simple import simple_test_task
    
    # 注册图像处理任务 - 使用显式任务名称修复Windows兼容性问题
    sharpen_image = celery_app.task(bind=True, name='tasks.image_sharpen.sharpen_image_task')(sharpen_image_task)
    gamma_correction = celery_app.task(bind=True, name='tasks.image_gamma_correction.gamma_correction_task')(gamma_correction_task)
    grayscale_image = celery_app.task(bind=True, name='tasks.image_grayscale.grayscale_image_task')(grayscale_image_task)
    canny_edge_detection = celery_app.task(bind=True, name='tasks.image_edge_detection.canny_edge_detection_task')(canny_edge_detection_task)
    image_fusion = celery_app.task(bind=True, name='tasks.image_fusion.image_fusion_task')(image_fusion_task)
    
    # 注册高级图像处理任务
    image_stitching = celery_app.task(bind=True, name='tasks.image_stitching.image_stitching_task')(image_stitching_task)
    texture_transfer = celery_app.task(bind=True, name='tasks.texture_transfer.texture_transfer_task')(texture_transfer_task)

    # 注册美颜处理任务
    beauty_enhancement = celery_app.task(bind=True, name='tasks.beauty_processing.beauty_enhancement_task')(beauty_enhancement_task)

    # 注册视频处理任务（统一入口）
    process_video_task = celery_app.task(bind=True, name='tasks.video_tasks.process_video_task')(process_video_task)

    # 注册简单测试任务
    simple_test = celery_app.task(bind=True, name='tasks.test_simple.simple_test_task')(simple_test_task)
    
    # 创建任务字典以便导出
    tasks = {
        # 图像处理任务
        'sharpen_image': sharpen_image,
        'gamma_correction': gamma_correction,
        'grayscale_image': grayscale_image,
        'canny_edge_detection': canny_edge_detection,
        'image_fusion': image_fusion,
        
        # 高级图像处理任务
        'image_stitching': image_stitching,
        'texture_transfer': texture_transfer,

        # 美颜处理任务
        'beauty_enhancement': beauty_enhancement,
        
        # 视频处理任务（统一入口）
        'process_video_task': process_video_task,

        # 简单测试任务
        'simple_test': simple_test,
    }
    
    return tasks