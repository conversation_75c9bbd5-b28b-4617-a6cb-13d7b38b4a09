import React, { useEffect, useRef } from 'react'
import { Trash2, Terminal, Info, CheckCircle, AlertCircle } from 'lucide-react'
import './LogOutput.css'

const LogOutput = ({ logs, onClear }) => {
  const logContainerRef = useRef(null)

  // 自动滚动到底部
  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight
    }
  }, [logs])

  const getLogIcon = (type) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="log-icon success" />
      case 'error':
        return <AlertCircle className="log-icon error" />
      case 'info':
      default:
        return <Info className="log-icon info" />
    }
  }

  const getLogClass = (type) => {
    return `log-entry ${type}`
  }

  return (
    <div className="log-output">
      <div className="card">
        <div className="log-header">
          <div className="log-title">
            <Terminal className="terminal-icon" />
            <h3>日志输出</h3>
          </div>
          <button 
            className="btn btn-secondary clear-btn"
            onClick={onClear}
            disabled={logs.length === 0}
          >
            <Trash2 className="btn-icon" />
            清除
          </button>
        </div>

        <div className="log-container" ref={logContainerRef}>
          {logs.length === 0 ? (
            <div className="log-empty">
              <Terminal className="empty-terminal-icon" />
              <p>暂无日志输出</p>
              <p className="empty-description">操作日志将在这里显示</p>
            </div>
          ) : (
            <div className="log-list">
              {logs.map((log) => (
                <div key={log.id} className={getLogClass(log.type)}>
                  <div className="log-time">{log.timestamp}</div>
                  <div className="log-content">
                    {getLogIcon(log.type)}
                    <span className="log-message">{log.message}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="log-footer">
          <span className="log-count">
            共 {logs.length} 条日志
          </span>
        </div>
      </div>
    </div>
  )
}

export default LogOutput
